"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.createTable('app_stage_activity_logs', {
        app_stage_activity_log_id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        user_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: 'References staff.staff_id, company.company_id, or admin.admin_id based on user_type'
        },
        user_type: {
          type: Sequelize.ENUM('staff', 'company', 'admin'),
          allowNull: false,
          comment: 'Type of user performing the action'
        },
        staff_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: 'References staff.staff_id when user_type is staff'
        },
        company_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: 'References company.company_id when user_type is company'
        },
        admin_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: 'References admin.admin_id when user_type is admin'
        },
        shipment_job_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          comment: 'References shipment_job.shipment_job_id'
        },
        stage_number: {
          type: Sequelize.INTEGER,
          allowNull: false,
          comment: 'Stage number (1=Add Items to Inventory, 2=Add Items to Storage, 3=Remove Items from Storage, 4=Remove Items from Inventory)'
        },
        stage_name: {
          type: Sequelize.STRING(100),
          allowNull: true,
          comment: 'Name of the stage for reference'
        },
        action_type: {
          type: Sequelize.ENUM(
            'stage_access',
            'add_items_selected',
            'view_items_selected',
            'remove_items_selected',
            'unit_manual_selection',
            'unit_qr_scan',
            'item_manual_selection',
            'item_qr_scan',
            'item_added',
            'item_edited',
            'item_duplicated',
            'item_deleted',
            'items_assigned_to_storage',
            'items_removed_from_storage',
            'items_removed_from_inventory'
          ),
          allowNull: false,
          comment: 'Specific action performed in the stage'
        },
        items_count: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: 'Number of items affected by the action'
        },
        selection_method: {
          type: Sequelize.ENUM('manual', 'qr_scan'),
          allowNull: true,
          comment: 'Method used for item/unit selection'
        },
        qr_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: 'References qr_code.qr_id when QR code is scanned'
        },
        unit_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: 'References unit_list.unit_id when unit is involved'
        },
        shipment_inventory_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: 'References shipment_inventory.shipment_inventory_id when specific item is involved'
        },
        action_details: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: 'JSON string containing detailed information about the action (fields modified, values, etc.)'
        },
        ip_address: {
          type: Sequelize.STRING(45),
          allowNull: true,
          comment: 'IP address of the user'
        },
        device_info: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: 'Device information (user agent, device type, etc.)'
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
        }
      }, {
        indexes: [
          {
            fields: ['user_id', 'user_type']
          },
          {
            fields: ['staff_id']
          },
          {
            fields: ['company_id']
          },
          {
            fields: ['admin_id']
          },
          {
            fields: ['shipment_job_id']
          },
          {
            fields: ['stage_number']
          },
          {
            fields: ['action_type']
          },
          {
            fields: ['created_at']
          },
          {
            fields: ['shipment_job_id', 'stage_number', 'created_at']
          },
          {
            fields: ['qr_id']
          },
          {
            fields: ['unit_id']
          },
          {
            fields: ['shipment_inventory_id']
          }
        ]
      });
      
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.dropTable('app_stage_activity_logs');
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  }
};
