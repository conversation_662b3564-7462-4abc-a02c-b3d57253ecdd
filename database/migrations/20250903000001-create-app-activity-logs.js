"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.createTable('app_activity_logs', {
        app_activity_log_id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        staff_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          comment: 'References staff.staff_id - only staff can login to APP'
        },
        shipment_job_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: 'References shipment_job.shipment_job_id'
        },
        activity_type: {
          type: Sequelize.ENUM(
            'login',
            'logout', 
            'stage_access',
            'shipment_access',
            'item_add',
            'item_edit',
            'item_duplicate',
            'item_delete',
            'storage_add',
            'storage_remove',
            'inventory_remove',
            'unit_mapping',
            'unit_move',
            'qr_scan',
            'manual_selection'
          ),
          allowNull: false,
          comment: 'Type of activity performed'
        },
        stage_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: 'Stage being accessed or modified'
        },
        stage_name: {
          type: Sequelize.STRING(100),
          allowNull: true,
          comment: 'Name of the stage for reference'
        },
        action_details: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: 'JSON string containing detailed information about the action'
        },
        ip_address: {
          type: Sequelize.STRING(45),
          allowNull: true,
          comment: 'IP address of the user'
        },
        device_info: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: 'Device information (user agent, device type, etc.)'
        },
        session_id: {
          type: Sequelize.STRING(255),
          allowNull: true,
          comment: 'Session identifier'
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
        }
      }, {
        indexes: [
          {
            fields: ['staff_id']
          },
          {
            fields: ['shipment_job_id']
          },
          {
            fields: ['activity_type']
          },
          {
            fields: ['created_at']
          },
          {
            fields: ['staff_id', 'activity_type', 'created_at']
          }
        ]
      });
      
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.dropTable('app_activity_logs');
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  }
};
