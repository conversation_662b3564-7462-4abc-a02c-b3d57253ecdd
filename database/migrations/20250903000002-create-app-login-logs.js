"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.createTable('app_login_logs', {
        app_login_log_id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        staff_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          comment: 'References staff.staff_id - only staff can login to APP'
        },
        login_type: {
          type: Sequelize.ENUM('login', 'logout', 'failed_login'),
          allowNull: false,
          comment: 'Type of login event'
        },
        email: {
          type: Sequelize.STRING(255),
          allowNull: true,
          comment: 'Email used for login attempt'
        },
        ip_address: {
          type: Sequelize.STRING(45),
          allowNull: true,
          comment: 'IP address of the login attempt'
        },
        device_info: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: 'Device information (user agent, device type, etc.)'
        },
        session_id: {
          type: Sequelize.STRING(255),
          allowNull: true,
          comment: 'Session identifier for successful logins'
        },
        access_token: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: 'Access token generated for successful logins'
        },
        failure_reason: {
          type: Sequelize.STRING(255),
          allowNull: true,
          comment: 'Reason for failed login attempts'
        },
        login_timestamp: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
          comment: 'Timestamp of the login/logout event'
        },
        logout_timestamp: {
          type: Sequelize.DATE,
          allowNull: true,
          comment: 'Timestamp of logout (for logout events)'
        },
        session_duration: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: 'Session duration in seconds (calculated on logout)'
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
        }
      }, {
        indexes: [
          {
            fields: ['staff_id']
          },
          {
            fields: ['login_type']
          },
          {
            fields: ['login_timestamp']
          },
          {
            fields: ['email']
          },
          {
            fields: ['ip_address']
          }
        ]
      });
      
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.dropTable('app_login_logs');
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  }
};
