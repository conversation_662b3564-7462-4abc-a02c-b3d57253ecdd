"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.createTable('app_item_activity_logs', {
        app_item_activity_log_id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        user_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: 'References staff.staff_id, company.company_id, or admin.admin_id based on user_type'
        },
        user_type: {
          type: Sequelize.ENUM('staff', 'company', 'admin'),
          allowNull: false,
          comment: 'Type of user performing the action'
        },
        staff_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: 'References staff.staff_id when user_type is staff'
        },
        company_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: 'References company.company_id when user_type is company'
        },
        admin_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: 'References admin.admin_id when user_type is admin'
        },
        shipment_job_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          comment: 'References shipment_job.shipment_job_id'
        },
        shipment_inventory_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: 'References shipment_inventory.shipment_inventory_id'
        },
        action_type: {
          type: Sequelize.ENUM(
            'item_added',
            'item_edited',
            'item_duplicated',
            'item_deleted',
            'item_assigned_to_storage',
            'item_removed_from_storage',
            'item_removed_from_inventory',
            'item_mapped_to_unit',
            'item_moved_with_unit'
          ),
          allowNull: false,
          comment: 'Type of action performed on the item'
        },
        stage_number: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: 'Stage number where action was performed'
        },
        item_name: {
          type: Sequelize.STRING(255),
          allowNull: true,
          comment: 'Name of the item for reference'
        },
        fields_modified: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: 'JSON array of field names that were modified'
        },
        old_values: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: 'JSON object containing old values of modified fields'
        },
        new_values: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: 'JSON object containing new values of modified fields'
        },
        qr_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: 'References qr_code.qr_id when QR code is involved'
        },
        unit_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: 'References unit_list.unit_id when unit is involved'
        },
        room_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: 'References shipment_room.room_id when room is involved'
        },
        selection_method: {
          type: Sequelize.ENUM('manual', 'qr_scan'),
          allowNull: true,
          comment: 'Method used for item selection'
        },
        source_location: {
          type: Sequelize.STRING(100),
          allowNull: true,
          comment: 'Source location (e.g., inventory, storage, staging)'
        },
        destination_location: {
          type: Sequelize.STRING(100),
          allowNull: true,
          comment: 'Destination location (e.g., storage, inventory, racks, vaults)'
        },
        item_weight: {
          type: Sequelize.DECIMAL(11, 2),
          allowNull: true,
          comment: 'Weight of the item'
        },
        item_volume: {
          type: Sequelize.DECIMAL(11, 2),
          allowNull: true,
          comment: 'Volume of the item'
        },
        action_details: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: 'Additional details about the action in JSON format'
        },
        ip_address: {
          type: Sequelize.STRING(45),
          allowNull: true,
          comment: 'IP address of the user'
        },
        device_info: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: 'Device information (user agent, device type, etc.)'
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
        }
      }, {
        indexes: [
          {
            fields: ['user_id', 'user_type']
          },
          {
            fields: ['staff_id']
          },
          {
            fields: ['company_id']
          },
          {
            fields: ['admin_id']
          },
          {
            fields: ['shipment_job_id']
          },
          {
            fields: ['shipment_inventory_id']
          },
          {
            fields: ['action_type']
          },
          {
            fields: ['stage_number']
          },
          {
            fields: ['created_at']
          },
          {
            fields: ['shipment_job_id', 'action_type', 'created_at']
          },
          {
            fields: ['qr_id']
          },
          {
            fields: ['unit_id']
          },
          {
            fields: ['room_id']
          }
        ]
      });
      
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.dropTable('app_item_activity_logs');
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  }
};
